<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GhostCoder Downloads</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .download-btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(45deg, #F8E71C, #FFD700);
            color: black;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .download-btn:hover {
            transform: scale(1.05);
        }
        .info {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 GhostCoder Download Test</h1>
    
    <div class="info">
        <h3>Direct Google Drive Downloads</h3>
        <p>These links will download your files directly from Google Drive:</p>
    </div>

    <div>
        <h3>Windows Installer</h3>
        <a href="https://drive.google.com/uc?export=download&id=1xBPo-9W9SX4gMKeLLVeLIZHKoK5ptRKq" 
           class="download-btn" 
           download="GhostCoder Setup 1.1.0.exe">
            💻 Download for Windows
        </a>
        <p><small>File ID: 1xBPo-9W9SX4gMKeLLVeLIZHKoK5ptRKq</small></p>
    </div>

    <div>
        <h3>macOS Installer</h3>
        <a href="https://drive.google.com/uc?export=download&id=1CFOWm9NdUrCahrYaldtfVi7KY_h1Wc6c" 
           class="download-btn" 
           download="GhostCoder-1.1.0.dmg">
            🚀 Download for macOS
        </a>
        <p><small>File ID: 1CFOWm9NdUrCahrYaldtfVi7KY_h1Wc6c</small></p>
    </div>

    <div class="info">
        <h3>✅ What to expect:</h3>
        <ul>
            <li>Click the button → File should start downloading immediately</li>
            <li>If you see a Google Drive preview page, the file isn't set to public</li>
            <li>Large files may take a moment to start downloading</li>
        </ul>
        
        <h3>🔧 If downloads don't work:</h3>
        <ol>
            <li>Make sure files are set to "Anyone with the link" in Google Drive</li>
            <li>Check that the files haven't been moved or deleted</li>
            <li>Try opening the direct URLs in a new tab</li>
        </ol>
    </div>

    <script>
        // Add click tracking
        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                console.log('Download clicked:', this.textContent);
                // You can add analytics tracking here
            });
        });
    </script>
</body>
</html>