# Git LFS configuration for large files
# Track executable files with Git LFS
*.exe filter=lfs diff=lfs merge=lfs -text
*.dmg filter=lfs diff=lfs merge=lfs -text
*.msi filter=lfs diff=lfs merge=lfs -text
*.pkg filter=lfs diff=lfs merge=lfs -text
*.deb filter=lfs diff=lfs merge=lfs -text
*.rpm filter=lfs diff=lfs merge=lfs -text
*.appimage filter=lfs diff=lfs merge=lfs -text
# Track other large binary files
*.zip filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.7z filter=lfs diff=lfs merge=lfs -text
# Track large media files
*.mp4 filter=lfs diff=lfs merge=lfs -text
*.mov filter=lfs diff=lfs merge=lfs -text
*.avi filter=lfs diff=lfs merge=lfs -text
*.mkv filter=lfs diff=lfs merge=lfs -text
*.psd filter=lfs diff=lfs merge=lfs -text
*.ai filter=lfs diff=lfs merge=lfs -text
# Track files in specific directories
public/downloads/* filter=lfs diff=lfs merge=lfs -text
public/downloads/*.exe filter=lfs diff=lfs merge=lfs -text
public/downloads/*.dmg filter=lfs diff=lfs merge=lfs -text
public/download/*.exe filter=lfs diff=lfs merge=lfs -text
public/download/*.dmg filter=lfs diff=lfs merge=lfs -text
src/asets/exe[[:space:]]file/*.exe filter=lfs diff=lfs merge=lfs -text
src/asets/exe[[:space:]]file/*.dmg filter=lfs diff=lfs merge=lfs -text
